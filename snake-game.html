<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
        }

        .game-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        h1 {
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .score-board {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }

        #gameCanvas {
            border: 3px solid #fff;
            border-radius: 10px;
            background: #000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }

        .controls {
            margin-top: 20px;
            color: white;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            display: none;
        }

        .instructions {
            margin-top: 15px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🐍 贪吃蛇游戏</h1>
        
        <div class="score-board">
            <div>分数: <span id="score">0</span></div>
            <div>最高分: <span id="highScore">0</span></div>
        </div>
        
        <canvas id="gameCanvas" width="400" height="400"></canvas>
        
        <div class="controls">
            <button class="control-btn" onclick="startGame()">开始游戏</button>
            <button class="control-btn" onclick="pauseGame()">暂停</button>
            <button class="control-btn" onclick="resetGame()">重新开始</button>
        </div>
        
        <div class="instructions">
            使用 ↑↓←→ 方向键或 WASD 控制蛇的移动<br>
            吃到红色食物得分，撞墙或撞到自己游戏结束
        </div>
    </div>

    <div class="game-over" id="gameOver">
        <h2>游戏结束!</h2>
        <p>最终分数: <span id="finalScore">0</span></p>
        <button class="control-btn" onclick="resetGame()">再玩一次</button>
    </div>

    <script>
        // 游戏配置
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const scoreElement = document.getElementById('score');
        const highScoreElement = document.getElementById('highScore');
        const gameOverElement = document.getElementById('gameOver');
        const finalScoreElement = document.getElementById('finalScore');

        // 游戏变量
        const gridSize = 20;
        const tileCount = canvas.width / gridSize;

        let snake = [
            {x: 10, y: 10}
        ];
        let food = {};
        let dx = 0;
        let dy = 0;
        let score = 0;
        let highScore = localStorage.getItem('snakeHighScore') || 0;
        let gameRunning = false;
        let gameLoop;

        // 初始化游戏
        function initGame() {
            highScoreElement.textContent = highScore;
            generateFood();
            drawGame();
        }

        // 生成食物
        function generateFood() {
            food = {
                x: Math.floor(Math.random() * tileCount),
                y: Math.floor(Math.random() * tileCount)
            };
            
            // 确保食物不在蛇身上生成
            for (let segment of snake) {
                if (segment.x === food.x && segment.y === food.y) {
                    generateFood();
                    return;
                }
            }
        }

        // 绘制游戏
        function drawGame() {
            // 清空画布
            ctx.fillStyle = 'black';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制蛇
            ctx.fillStyle = '#4CAF50';
            for (let segment of snake) {
                ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize - 2, gridSize - 2);
            }

            // 绘制蛇头（不同颜色）
            if (snake.length > 0) {
                ctx.fillStyle = '#8BC34A';
                ctx.fillRect(snake[0].x * gridSize, snake[0].y * gridSize, gridSize - 2, gridSize - 2);
            }

            // 绘制食物
            ctx.fillStyle = '#FF5722';
            ctx.fillRect(food.x * gridSize, food.y * gridSize, gridSize - 2, gridSize - 2);
        }

        // 更新游戏状态
        function updateGame() {
            if (!gameRunning) return;

            // 如果没有移动方向，不更新游戏状态
            if (dx === 0 && dy === 0) return;

            const head = {x: snake[0].x + dx, y: snake[0].y + dy};

            // 检查边界碰撞
            if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
                gameOver();
                return;
            }

            // 检查自身碰撞
            for (let segment of snake) {
                if (head.x === segment.x && head.y === segment.y) {
                    gameOver();
                    return;
                }
            }

            snake.unshift(head);

            // 检查是否吃到食物
            if (head.x === food.x && head.y === food.y) {
                score += 10;
                scoreElement.textContent = score;
                generateFood();
            } else {
                snake.pop();
            }

            drawGame();
        }

        // 开始游戏
        function startGame() {
            if (gameRunning) return;
            
            gameRunning = true;
            gameOverElement.style.display = 'none';
            
            if (gameLoop) clearInterval(gameLoop);
            gameLoop = setInterval(updateGame, 400);
        }

        // 暂停游戏
        function pauseGame() {
            gameRunning = !gameRunning;
            if (gameRunning) {
                if (gameLoop) clearInterval(gameLoop);
                gameLoop = setInterval(updateGame, 400);
            } else {
                clearInterval(gameLoop);
            }
        }

        // 重置游戏
        function resetGame() {
            clearInterval(gameLoop);
            gameRunning = false;
            
            snake = [{x: 10, y: 10}];
            dx = 0;
            dy = 0;
            score = 0;
            scoreElement.textContent = score;
            
            generateFood();
            drawGame();
            gameOverElement.style.display = 'none';
        }

        // 游戏结束
        function gameOver() {
            gameRunning = false;
            clearInterval(gameLoop);
            
            if (score > highScore) {
                highScore = score;
                highScoreElement.textContent = highScore;
                localStorage.setItem('snakeHighScore', highScore);
            }
            
            finalScoreElement.textContent = score;
            gameOverElement.style.display = 'block';
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (!gameRunning) return;

            const key = e.key.toLowerCase();
            
            // 防止反向移动
            if ((key === 'arrowleft' || key === 'a') && dx !== 1) {
                dx = -1;
                dy = 0;
            } else if ((key === 'arrowup' || key === 'w') && dy !== 1) {
                dx = 0;
                dy = -1;
            } else if ((key === 'arrowright' || key === 'd') && dx !== -1) {
                dx = 1;
                dy = 0;
            } else if ((key === 'arrowdown' || key === 's') && dy !== -1) {
                dx = 0;
                dy = 1;
            }
        });

        // 初始化游戏
        initGame();
    </script>
</body>
</html>